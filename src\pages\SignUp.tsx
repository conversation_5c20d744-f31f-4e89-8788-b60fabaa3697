import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Scissors, Mail, Lock, User, Building2, MapPin, ArrowRight, CheckCircle2, AlertCircle, Eye, EyeOff } from 'lucide-react';
import { useAuth } from '../contexts/SimpleAuthContext';

export function SignUp() {
  const navigate = useNavigate();
  const { signup, loading: authLoading } = useAuth();

  // Form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [salonName, setSalonName] = useState('');
  const [address, setAddress] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  // UI state
  const [error, setError] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);
  const [emailConfirmationSent, setEmailConfirmationSent] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [forceRender, setForceRender] = useState(0);

  // Add a ref to track if signup was successful
  const signupSuccessRef = useRef(false);

  // Monitor state changes and restore if needed - CRITICAL FIX
  useEffect(() => {
    console.log('useEffect triggered - showSuccess:', showSuccess, 'emailConfirmationSent:', emailConfirmationSent, 'signupSuccessRef:', signupSuccessRef.current, 'authLoading:', authLoading);

    // If signup was successful but states got reset, restore them immediately
    if (signupSuccessRef.current && (!showSuccess || !emailConfirmationSent)) {
      console.log('🔄 RESTORING success state that was reset by auth context!');
      setShowSuccess(true);
      setEmailConfirmationSent(true);
      setForceRender(prev => prev + 1);
    }
  }, [showSuccess, emailConfirmationSent, authLoading]);

  // CRITICAL: Monitor auth loading changes specifically
  useEffect(() => {
    console.log('🔍 Auth loading changed:', authLoading, 'signupSuccessRef:', signupSuccessRef.current);

    // When auth loading goes from true to false after successful signup, restore success state
    if (!authLoading && signupSuccessRef.current) {
      console.log('🚨 Auth loading finished, ensuring success screen shows');
      setTimeout(() => {
        setShowSuccess(true);
        setEmailConfirmationSent(true);
        setForceRender(prev => prev + 1);
      }, 10); // Small delay to ensure auth context state is settled
    }
  }, [authLoading]);

  // Additional effect to monitor just the success state
  useEffect(() => {
    if (signupSuccessRef.current) {
      console.log('🎯 Success ref is true, ensuring success screen shows');
      if (!showSuccess) {
        console.log('🔧 Fixing showSuccess state');
        setShowSuccess(true);
        setForceRender(prev => prev + 1);
      }
      if (!emailConfirmationSent) {
        console.log('🔧 Fixing emailConfirmationSent state');
        setEmailConfirmationSent(true);
        setForceRender(prev => prev + 1);
      }
    }
  }, [signupSuccessRef.current, showSuccess, emailConfirmationSent]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsSubmitting(true);

    // Don't reset these states here - they should persist once set
    // setEmailConfirmationSent(false);

    // Client-side validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address.');
      return;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters long.');
      return;
    }

    if (!firstName.trim()) {
      setError('First name is required.');
      return;
    }

    if (!lastName.trim()) {
      setError('Last name is required.');
      return;
    }

    if (!salonName.trim()) {
      setError('Salon name is required.');
      return;
    }

    try {
      // Create new account
      const userData = {
        first_name: firstName,
        last_name: lastName,
        salon_name: salonName,
        salon_address: address,
      };

      console.log('Starting signup process...');
      const { error, user } = await signup(email, password, userData);

      console.log('Signup response:', { error, user });

      if (error) {
        throw new Error(error.message || 'Account creation failed. Please try again.');
      }

      // If we reach here, signup was successful
      console.log('Signup successful, showing success screen');

      // Since you mentioned that confirmation emails are being sent,
      // we'll assume email confirmation is required and show the appropriate message
      signupSuccessRef.current = true;

      console.log('Setting success states...');

      // Set states directly and immediately
      setEmailConfirmationSent(true);
      setShowSuccess(true);
      setForceRender(prev => prev + 1);

      // Also try using a timeout to ensure the state updates are processed
      setTimeout(() => {
        console.log('🚀 Timeout check - forcing success screen');
        setShowSuccess(true);
        setEmailConfirmationSent(true);
      }, 0);

      console.log('Success state set - ref marked as true, states should update on next render');
    } catch (error) {
      console.error('Signup error:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSuccessContinue = () => {
    if (emailConfirmationSent) {
      // If email confirmation is required, go to login
      navigate('/auth');
    } else {
      // Otherwise, go to dashboard
      navigate('/dashboard');
    }
  };

  const handleBackToLogin = () => {
    navigate('/auth');
  };

  // Debug logging
  console.log('🔍 SignUp RENDER - showSuccess:', showSuccess, 'emailConfirmationSent:', emailConfirmationSent, 'isSubmitting:', isSubmitting, 'authLoading:', authLoading, 'signupSuccessRef:', signupSuccessRef.current);

  // Success screen after account creation
  if (showSuccess) {
    console.log('✅ RENDERING SUCCESS SCREEN');
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 to-purple-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10 text-center space-y-6">
            <div className="flex justify-center">
              <CheckCircle2 className="h-16 w-16 text-green-500" />
            </div>

            {emailConfirmationSent ? (
              <>
                <h2 className="text-2xl font-bold text-gray-900">Check Your Email!</h2>
                <p className="text-gray-600">Your salon account has been created successfully.</p>

                <div className="bg-gradient-to-r from-yellow-50 to-orange-50 p-6 rounded-lg border-2 border-yellow-200 text-left">
                  <div className="flex items-center mb-3">
                    <Mail className="h-6 w-6 text-yellow-600 mr-2" />
                    <h3 className="text-lg font-semibold text-yellow-800">Email Confirmation Required</h3>
                  </div>
                  <p className="text-sm text-yellow-700 mb-3">
                    We've sent a confirmation link to:
                  </p>
                  <p className="text-base font-medium text-yellow-800 bg-yellow-100 px-3 py-2 rounded mb-3">
                    {email}
                  </p>
                  <p className="text-sm text-yellow-700">
                    Please check your inbox (and spam folder) and click the confirmation link to activate your account before signing in.
                  </p>
                </div>
              </>
            ) : (
              <>
                <h2 className="text-2xl font-bold text-gray-900">Welcome aboard!</h2>
                <p className="text-gray-600">Your salon account has been created successfully.</p>
              </>
            )}

            {!emailConfirmationSent && (
              <div className="bg-blue-50 p-4 rounded-md text-left">
                <h3 className="text-md font-medium text-blue-800 mb-1">What's been set up for you:</h3>
                <ul className="text-sm text-blue-700 list-disc pl-5 space-y-1">
                  <li>Your salon: <span className="font-medium">{salonName}</span></li>
                  <li>Default services catalog with common salon services</li>
                  <li>Basic appointment management system</li>
                </ul>
              </div>
            )}

            <button
              onClick={handleSuccessContinue}
              className="w-full flex justify-center items-center px-4 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              {emailConfirmationSent ? 'Go to Login Page' : 'Continue to Dashboard'}
              <ArrowRight className="ml-2 h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 to-purple-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="h-12 w-12 bg-indigo-600 rounded-lg flex items-center justify-center">
            <Scissors className="h-8 w-8 text-white" />
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Create your salon account
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Get started with your salon management
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value);
                    if (error) setError(null);
                  }}
                  className="appearance-none block w-full pl-10 px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  autoComplete="new-password"
                  required
                  value={password}
                  onChange={(e) => {
                    setPassword(e.target.value);
                    if (error) setError(null);
                  }}
                  className="appearance-none block w-full pl-10 pr-10 px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="••••••••"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5" />
                    ) : (
                      <Eye className="h-5 w-5" />
                    )}
                  </button>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
              <div>
                <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
                  First name
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    name="firstName"
                    id="firstName"
                    autoComplete="given-name"
                    required
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    className="appearance-none block w-full pl-10 px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
                  Last name
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <User className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    name="lastName"
                    id="lastName"
                    autoComplete="family-name"
                    required
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    className="appearance-none block w-full pl-10 px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  />
                </div>
              </div>
            </div>

            <div>
              <label htmlFor="salonName" className="block text-sm font-medium text-gray-700">
                Salon name
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Building2 className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  name="salonName"
                  id="salonName"
                  required
                  value={salonName}
                  onChange={(e) => setSalonName(e.target.value)}
                  className="appearance-none block w-full pl-10 px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="Your Salon Name"
                />
              </div>
            </div>

            <div>
              <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                Salon address (optional)
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MapPin className="h-5 w-5 text-gray-400" />
                </div>
                <textarea
                  id="address"
                  name="address"
                  rows={3}
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  className="appearance-none block w-full pl-10 px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="Enter your salon's address"
                />
              </div>
            </div>

            {error && (
              <div className="rounded-md bg-red-50 p-4 border-2 border-red-200 mb-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertCircle className="h-6 w-6 text-red-500" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-base font-semibold text-red-800 mb-1">Signup Error</h3>
                    <p className="text-sm text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-2">
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Creating account...' : 'Create account'}
              </button>

              {/* Test button to manually trigger success screen */}
              <button
                type="button"
                onClick={() => {
                  console.log('🧪 TEST: Manually triggering success screen');
                  signupSuccessRef.current = true;
                  setEmailConfirmationSent(true);
                  setShowSuccess(true);
                  setForceRender(prev => prev + 1);
                }}
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                🧪 TEST: Show Success Screen
              </button>
            </div>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">
                  Already have an account?
                </span>
              </div>
            </div>

            <div className="mt-6">
              <button
                onClick={handleBackToLogin}
                className="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Sign in to existing account
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}